//command.client.tsx
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Command } from 'cmdk';
import { cn } from '../../../../lib/utils';
import {
  Search,
  User,
  Calendar,
  BookOpen,
  Users,
  Settings,
  FileText,
  Plus,
  Home,
  Bell,
  TrendingUp,
  Clock,
  Star,
  ChevronRight,
  FolderPlus,
  Folder,
  HelpCircle,
  MessageSquare,
  UserPlus,
  GraduationCap,
  ArrowLeft,
  Check,
  X,
  Mail,
  AtSign,
  UserCheck
} from 'lucide-react';
import { Switch } from '../../../animate-ui/base/switch';
import {
  CommandInputProvider,
  CommandInput,
  CommandInputField,
  CommandInputSubmit,
} from './command-input';

// Command menu modes
type CommandMode = 'default' | 'add-student' | 'add-class';

// Student form step type
type StudentFormStep = 'name' | 'email';

// Interface for student data
interface PendingStudent {
  id: string;
  name: string;
  email: string;
  generateUsername: boolean;
}

// Dynamic command data structure for your command menu
const getCommandData = (userRole?: string) => ({
  quickActions: [
    // Teacher-specific actions
    ...(userRole === 'teacher' ? [
      { id: 'add-student', label: 'Add Student', icon: UserPlus, keywords: ['add', 'create', 'new', 'student', 'invite'], type: 'dialog' },
      { id: 'add-class', label: 'Add Class', icon: GraduationCap, keywords: ['add', 'create', 'new', 'class', 'grade', 'level'], type: 'dialog' },
    ] : []),
    // General actions
    { id: 'new-note', label: 'New Note', icon: FileText, keywords: ['create', 'new', 'note'] },
    { id: 'new-folder', label: 'New Folder', icon: FolderPlus, keywords: ['create', 'new', 'folder'] },
    { id: 'settings', label: 'Settings', icon: Settings, keywords: ['settings', 'preferences'] },
  ],
  navigation: [
    { id: 'home', label: 'Dashboard', icon: Home, url: '', keywords: ['home', 'dashboard'] },
    ...(userRole === 'teacher' ? [
      { id: 'students', label: 'Students', icon: Users, url: 'students', keywords: ['students', 'manage'] },
      { id: 'classes', label: 'Classes', icon: GraduationCap, url: 'classes', keywords: ['classes', 'grades'] },
    ] : []),
    { id: 'notes', label: 'Notes', icon: FileText, url: 'notes', keywords: ['notes', 'documents'] },
    { id: 'folders', label: 'Folders', icon: Folder, url: 'folders', keywords: ['folders', 'organize'] },
  ],
  recent: [
    { id: 'recent-note-1', label: 'Recent Note 1', icon: Clock, url: 'notes/recent-note-1', keywords: ['recent', 'note'] },
    { id: 'recent-note-2', label: 'Recent Note 2', icon: Clock, url: 'notes/recent-note-2', keywords: ['recent', 'note'] },
  ],
  suggestions: [
    { id: 'help', label: 'Help & Support', icon: HelpCircle, keywords: ['help', 'support'] },
    { id: 'feedback', label: 'Send Feedback', icon: MessageSquare, keywords: ['feedback', 'contact'] },
  ]
});

interface CommandMenuProps {
  onHeightChange?: (height: number) => void;
  onSelect?: (item: any) => void;
  onClose?: () => void;
  hideSearchInput?: boolean; // Hide the search input
  searchValue?: string; // External search value
  onSearchChange?: (value: string) => void; // External search change handler
  userRole?: string; // User role for dynamic commands
  onDialogAction?: (dialogType: string) => void; // Handler for dialog actions
  onStudentAdd?: (student: PendingStudent) => void; // Handler for adding students
  onClassAdd?: (className: string) => void; // Handler for adding classes
  teacherClasses?: any[]; // Available teacher classes for assignment
  onModeChange?: (mode: CommandMode, placeholder: string, step?: string) => void; // Notify parent of mode changes
  onFormSubmit?: (submitHandler: () => void) => void; // Handler for external form submission (Enter key)
}

export function CommandMenu({
  onHeightChange,
  onSelect,
  onClose,
  hideSearchInput = false,
  searchValue: externalSearchValue = '',
  onSearchChange,
  userRole,
  onDialogAction,
  onStudentAdd,
  onClassAdd,
  teacherClasses = [],
  onModeChange,
  onFormSubmit
}: CommandMenuProps) {
  const [mounted, setMounted] = useState(false);
  const [internalSearchValue, setInternalSearchValue] = useState('');
  const [mode, setMode] = useState<CommandMode>('default');
  const [currentStep, setCurrentStep] = useState<StudentFormStep>('name');
  const [studentName, setStudentName] = useState('');
  const [studentEmail, setStudentEmail] = useState('');
  const [generateUsername, setGenerateUsername] = useState(true);
  const [className, setClassName] = useState('');
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [isUsingMouse, setIsUsingMouse] = useState(false);

  const resultsRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const commandRef = useRef<HTMLDivElement>(null);

  // Use external search value if provided, otherwise use internal
  const searchValue = hideSearchInput ? externalSearchValue : internalSearchValue;

  // Utility function to create username
  const createUsername = useCallback((name: string, email: string): string => {
    const namePart = name.toLowerCase().replace(/\s+/g, '');
    const emailPart = email.split('@')[0]?.toLowerCase() || '';
    const randomNum = Math.floor(Math.random() * 100).toString().padStart(2, '0');
    const baseUsername = namePart.length >= 3 ? namePart : emailPart;
    return `${baseUsername}${randomNum}`;
  }, []);

  // Reset form state
  const resetForm = useCallback(() => {
    setStudentName('');
    setStudentEmail('');
    setClassName('');
    setCurrentStep('name');
    setMessage('');
    setMessageType('');
  }, []);

  // Go back to default mode
  const goBackToDefault = useCallback(() => {
    setMode('default');
    resetForm();
    if (onSearchChange) {
      onSearchChange('');
    } else {
      setInternalSearchValue('');
    }

    // Notify parent of mode change back to default
    if (onModeChange) {
      onModeChange('default', 'Type a command or search...', undefined);
    }
  }, [resetForm, onSearchChange, onModeChange]);

  // Handle mode changes
  const handleModeChange = useCallback((newMode: CommandMode) => {
    setMode(newMode);
    resetForm();
    if (onSearchChange) {
      onSearchChange('');
    } else {
      setInternalSearchValue('');
    }

    // Notify parent of mode change with appropriate placeholder
    if (onModeChange) {
      let placeholder = 'Type a command or search...';
      let step = undefined;
      if (newMode === 'add-student') {
        placeholder = 'Enter student name...';
        step = 'name';
      } else if (newMode === 'add-class') {
        placeholder = 'Enter class name...';
      }
      onModeChange(newMode, placeholder, step);
    }
  }, [resetForm, onSearchChange, onModeChange]);

  // Handle student form submission
  const handleStudentFormSubmit = useCallback(() => {
    if (currentStep === 'name') {
      if (!studentName.trim()) {
        setMessage('Please enter a student name');
        setMessageType('error');
        return;
      }
      setCurrentStep('email');
      setMessage('');
      setMessageType('');

      // Clear the search input for email step
      if (onSearchChange) {
        onSearchChange('');
      } else {
        setInternalSearchValue('');
      }

      // Update placeholder for email step
      if (onModeChange) {
        onModeChange('add-student', 'Enter student email...', 'email');
      }
    } else if (currentStep === 'email') {
      if (!studentEmail.trim() || !studentEmail.includes('@')) {
        setMessage('Please enter a valid email address');
        setMessageType('error');
        return;
      }

      // Create student object
      const newStudent: PendingStudent = {
        id: Date.now().toString(),
        name: studentName.trim(),
        email: studentEmail.trim(),
        generateUsername
      };

      // Show loading message
      setMessage('Creating student account...');
      setMessageType('');

      // Call the parent handler
      if (onStudentAdd) {
        onStudentAdd(newStudent);
      }
    }
  }, [currentStep, studentName, studentEmail, generateUsername, onStudentAdd, goBackToDefault, onClose]);

  // Handle class form submission
  const handleClassFormSubmit = useCallback(() => {
    if (!className.trim()) {
      setMessage('Please enter a class name');
      setMessageType('error');
      return;
    }

    // Call the parent handler
    if (onClassAdd) {
      onClassAdd(className.trim());
    }

    setMessage('Class added successfully!');
    setMessageType('success');

    // Reset form and go back to default after a short delay
    setTimeout(() => {
      goBackToDefault();
      if (onClose) {
        onClose();
      }
    }, 1500);
  }, [className, onClassAdd, goBackToDefault, onClose]);

  // External form submission handler (called from mobile toolbar Enter key)
  const handleExternalFormSubmit = useCallback(() => {
    if (mode === 'add-student') {
      handleStudentFormSubmit();
    } else if (mode === 'add-class') {
      handleClassFormSubmit();
    }
  }, [mode, handleStudentFormSubmit, handleClassFormSubmit]);

  // Notify parent when mode changes to a form mode
  React.useEffect(() => {
    if (onFormSubmit && (mode === 'add-student' || mode === 'add-class')) {
      onFormSubmit(handleExternalFormSubmit);
    }
  }, [mode, onFormSubmit, handleExternalFormSubmit]);

  // Handle escape key to go back to default mode
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && (mode === 'add-student' || mode === 'add-class')) {
        e.preventDefault();
        goBackToDefault();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [mode, goBackToDefault]);

  // Listen for student creation events
  React.useEffect(() => {
    const handleStudentCreated = (event: CustomEvent) => {
      // Don't show success message in command menu - it shows in CommandInput button
      // Just reset form to add another student
      setTimeout(() => {
        setStudentName('');
        setStudentEmail('');
        setCurrentStep('name');
        setMessage('');
        setMessageType('');

        // Update placeholder for new student
        if (onModeChange) {
          onModeChange('add-student', 'Enter student name...', 'name');
        }

        // Clear search input
        if (onSearchChange) {
          onSearchChange('');
        } else {
          setInternalSearchValue('');
        }
      }, 500); // Shorter delay since no message to show
    };

    const handleStudentCreationError = (event: CustomEvent) => {
      const errorMessage = event.detail?.error || 'Failed to create student';
      setMessage(errorMessage);
      setMessageType('error');

      // Clear error message after 4 seconds
      setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 4000);
    };

    const handleExistingStudentFound = (event: CustomEvent) => {
      // Don't show success message in command menu - it shows in CommandInput button
      // Just reset form to add another student
      setTimeout(() => {
        setStudentName('');
        setStudentEmail('');
        setCurrentStep('name');
        setMessage('');
        setMessageType('');

        // Update placeholder for new student
        if (onModeChange) {
          onModeChange('add-student', 'Enter student name...', 'name');
        }

        // Clear search input
        if (onSearchChange) {
          onSearchChange('');
        } else {
          setInternalSearchValue('');
        }
      }, 500); // Shorter delay since no message to show
    };

    window.addEventListener('studentCreated', handleStudentCreated as EventListener);
    window.addEventListener('studentCreationError', handleStudentCreationError as EventListener);
    window.addEventListener('existingStudentFound', handleExistingStudentFound as EventListener);

    return () => {
      window.removeEventListener('studentCreated', handleStudentCreated as EventListener);
      window.removeEventListener('studentCreationError', handleStudentCreationError as EventListener);
      window.removeEventListener('existingStudentFound', handleExistingStudentFound as EventListener);
    };
  }, [onModeChange, onSearchChange]);

  useEffect(() => {
    setMounted(true);
  }, []);

  // CRITICAL: Focus management for immediate keyboard navigation
  useEffect(() => {
    if (!mounted || !commandRef.current) return;

    // Use a more aggressive focus strategy
    const focusInput = () => {
      // First try to find the CMDK input directly
      const cmdkInput = commandRef.current?.querySelector('[cmdk-input]') as HTMLInputElement;
      if (cmdkInput) {
        cmdkInput.focus();
        console.log('✅ CMDK input focused successfully');
        return true;
      }

      // Fallback to any input inside the command
      const anyInput = commandRef.current?.querySelector('input') as HTMLInputElement;
      if (anyInput) {
        anyInput.focus();
        console.log('✅ Fallback input focused');
        return true;
      }

      // Last resort: focus the command root itself
      if (commandRef.current) {
        commandRef.current.focus();
        console.log('✅ Command root focused');
        return true;
      }

      return false;
    };

    // Single focus attempt with a small delay
    const timeoutId = setTimeout(() => {
      if (!focusInput()) {
        console.warn('❌ Failed to focus command menu input');
      }
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [mounted]);

  // Monitor height changes of the results container
  useEffect(() => {
    if (!resultsRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const resultsHeight = entry.contentRect.height;
        // When hideSearchInput is true, we only report the results height
        // since the search input is handled externally
        const totalHeight = hideSearchInput ? resultsHeight : resultsHeight + 80;
        onHeightChange?.(totalHeight);
      }
    });

    resizeObserver.observe(resultsRef.current);
    return () => resizeObserver.disconnect();
  }, [onHeightChange, hideSearchInput]);

  // Get dynamic command data based on user role
  const commandData = getCommandData(userRole);

  const handleSelect = useCallback((value: string) => {
    if (mode === 'default') {
      const allItems = [
        ...commandData.quickActions,
        ...commandData.navigation,
        ...commandData.recent,
        ...commandData.suggestions
      ];

      const selectedItem = allItems.find(item => item.id === value);

      if (selectedItem) {
        // Handle mode changes for add actions
        if (selectedItem.id === 'add-student') {
          handleModeChange('add-student');
          return;
        } else if (selectedItem.id === 'add-class') {
          handleModeChange('add-class');
          return;
        } else if (onSelect) {
          // Handle regular actions
          onSelect(selectedItem);
          if (onClose) {
            onClose();
          }
        }
      }
    } else if (mode === 'add-student' || value === 'submit-student-form') {
      // Handle student form submission
      handleStudentFormSubmit();
    } else if (mode === 'add-class' || value === 'submit-class-form') {
      // Handle class form submission
      handleClassFormSubmit();
    }
  }, [mode, onSelect, onClose, commandData, handleModeChange]);

  // Handle internal search changes (when not using external input)
  const handleInternalSearchChange = (value: string) => {
    if (mode === 'add-student') {
      if (currentStep === 'name') {
        setStudentName(value);
      } else {
        setStudentEmail(value);
      }
    } else if (mode === 'add-class') {
      setClassName(value);
    } else {
      setInternalSearchValue(value);
    }
  };

  // Handle external search changes
  const handleExternalSearchChange = useCallback((value: string) => {
    if (mode === 'add-student') {
      if (currentStep === 'name') {
        setStudentName(value);
      } else {
        setStudentEmail(value);
      }
    } else if (mode === 'add-class') {
      setClassName(value);
    }
    if (onSearchChange) {
      onSearchChange(value);
    }
  }, [mode, currentStep, onSearchChange]);

  // Get the current input value based on mode
  const getCurrentInputValue = () => {
    if (mode === 'add-student') {
      return currentStep === 'name' ? studentName : studentEmail;
    } else if (mode === 'add-class') {
      return className;
    }
    return searchValue;
  };

  // Get placeholder text based on mode
  const getPlaceholderText = () => {
    if (mode === 'add-student') {
      return currentStep === 'name' ? 'Enter student name...' : 'Enter student email...';
    } else if (mode === 'add-class') {
      return 'Enter class name...';
    }
    return 'Type a command or search...';
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className="w-full flex flex-col">
      {/* Results Container - This is what ResizeObserver monitors */}
      <div 
        ref={resultsRef}
        className={`bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-700 shadow-lg overflow-hidden ${
          hideSearchInput ? 'rounded-lg' : 'rounded-t-lg border-b-0'
        }`}
        data-command-menu="true"
      >
        <Command
          ref={commandRef}
          className="bg-transparent border-none"
          data-command-menu="true"
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              e.preventDefault();
              onClose?.();
            }
            // Track keyboard navigation
            if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
              setIsUsingMouse(false);
            }
            // Let CMDK handle arrow keys naturally - don't interfere
          }}
          onMouseMove={() => {
            // Track mouse navigation
            setIsUsingMouse(true);
          }}
        >
          {/* The actual CMDK input - positioned off-screen but focusable */}
          <Command.Input
            ref={inputRef}
            placeholder={getPlaceholderText()}
            className="absolute -left-[9999px] w-px h-px opacity-0 pointer-events-none"
            value={getCurrentInputValue()}
            onValueChange={hideSearchInput ? handleExternalSearchChange : handleInternalSearchChange}
            autoFocus={true}
          />
          
          {/* Results area - with minimum height to prevent jumping */}
          <Command.List
            className="overflow-y-auto custom-scrollbar p-2"
            style={{
              maxHeight: '300px',
              minHeight: '50px' // Ensures container never gets smaller than empty state
            }}
          >
            {mode === 'default' && (
              <>
                <Command.Empty className="flex flex-col items-center justify-center py-8 text-center">
                 <div className="flex gap-2 items-center justify-center text-center">
                    <Search className="w-4 h-4 text-zinc-400 dark:text-zinc-500 mb-2" />
                  <p className="text-sm font-redaction-normal font-medium text-zinc-600 dark:text-zinc-400">
                    No results found
                  </p>
                 </div>

                  <p className="text-xs font-manrope text-zinc-500 dark:text-zinc-500 mt-1">
                    Try a different search term
                  </p>
                </Command.Empty>
              </>
            )}

            {/* Student Add Mode */}
            {mode === 'add-student' && (
              <div className="p-4 overflow-hidden">
                {/* Hidden command item for Enter key handling */}
                <Command.Item
                  value="submit-student-form"
                  onSelect={handleSelect}
                  className="hidden"
                />
                  <div className="flex items-center justify-between mb-4 min-w-0">
                    <div className="flex items-center gap-3">
                      <button
                        onClick={goBackToDefault}
                        className="flex items-center justify-center w-8 h-8 rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors"
                      >
                        <ArrowLeft className="w-4 h-4 text-zinc-600 dark:text-zinc-400" />
                      </button>
                      <div className="flex items-center gap-2">
                        <UserPlus className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        <h3 className="text-lg font-medium text-zinc-800 dark:text-zinc-200">
                          Add Student
                        </h3>
                      </div>
                    </div>

                  {/* Username/Email toggle in top right corner */}
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <span className="text-xs text-zinc-500 dark:text-zinc-400 whitespace-nowrap">
                      {generateUsername ? 'Generate username for login' : 'Use email for login'}
                    </span>
                    <div className="relative">
                      <Switch
                        checked={generateUsername}
                        onCheckedChange={setGenerateUsername}
                        className="w-12 h-6 bg-gradient-to-br from-zinc-100 to-zinc-200 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset] [&_[data-slot=switch-thumb]]:!bg-zinc-200 dark:[&_[data-slot=switch-thumb]]:!bg-zinc-800 [&_[data-slot=switch-thumb]]:!shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:[&_[data-slot=switch-thumb]]:!shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
                        thumbIcon={generateUsername ? <UserCheck className="w-3 h-3 text-black dark:text-white" /> : <AtSign className="w-3 h-3 text-black dark:text-white" />}
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="text-sm text-zinc-600 dark:text-zinc-400">
                    Step {currentStep === 'name' ? '1' : '2'} of 2: {currentStep === 'name' ? 'Enter student name' : 'Enter student email'}
                  </div>



                  {message && (
                    <div className={`p-3 rounded-lg text-sm ${
                      messageType === 'error'
                        ? 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400'
                        : 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400'
                    }`}>
                      {message}
                    </div>
                  )}

                  <div className="flex items-center gap-2 text-xs text-zinc-500 dark:text-zinc-400">
                    <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                      <span className="text-xs">↵</span>
                    </kbd>
                    <span>{currentStep === 'name' ? 'Continue to email' : 'Add student'}</span>
                    <span className="text-zinc-400">•</span>
                    <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                      <span className="text-xs">Esc</span>
                    </kbd>
                    <span>Back</span>
                  </div>
                </div>
              </div>
            )}

            {/* Class Add Mode */}
            {mode === 'add-class' && (
              <div className="p-4">
                {/* Hidden command item for Enter key handling */}
                <Command.Item
                  value="submit-class-form"
                  onSelect={handleSelect}
                  className="hidden"
                />
                <div className="flex items-center gap-3 mb-4">
                  <button
                    onClick={goBackToDefault}
                    className="flex items-center justify-center w-8 h-8 rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors"
                  >
                    <ArrowLeft className="w-4 h-4 text-zinc-600 dark:text-zinc-400" />
                  </button>
                  <div className="flex items-center gap-2">
                    <GraduationCap className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    <h3 className="text-lg font-medium text-zinc-800 dark:text-zinc-200">
                      Add Class
                    </h3>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="text-sm text-zinc-600 dark:text-zinc-400">
                    Enter the name for your new class
                  </div>

                  {message && (
                    <div className={`p-3 rounded-lg text-sm ${
                      messageType === 'error'
                        ? 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400'
                        : 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400'
                    }`}>
                      {message}
                    </div>
                  )}

                  <div className="flex items-center gap-2 text-xs text-zinc-500 dark:text-zinc-400">
                    <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                      <span className="text-xs">↵</span>
                    </kbd>
                    <span>Create class</span>
                    <span className="text-zinc-400">•</span>
                    <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                      <span className="text-xs">Esc</span>
                    </kbd>
                    <span>Back</span>
                  </div>
                </div>
              </div>
            )}

            {/* Default Mode - Show all command groups */}
            {mode === 'default' && (
              <>
                {/* Quick Actions Group */}
                <Command.Group heading="Quick Actions">
                  {commandData.quickActions.map((item) => (
                    <Command.Item
                      key={item.id}
                      value={item.id}
                      keywords={item.keywords}
                      onSelect={handleSelect}
                      className={cn(
                        "flex items-center gap-3 px-3 py-2.5 text-sm cursor-pointer rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors mx-1 mb-1",
                        // Fade keyboard selection when mouse is being used
                        isUsingMouse
                          ? "data-[selected=true]:bg-zinc-50 dark:data-[selected=true]:bg-zinc-800/50 data-[selected=true]:opacity-60"
                          : "data-[selected=true]:bg-zinc-100 dark:data-[selected=true]:bg-zinc-800"
                      )}
                    >
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-md">
                        <item.icon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="flex-1">
                        <span className="text-zinc-800 dark:text-zinc-200 font-medium">{item.label}</span>
                      </div>
                      <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded">
                        Action
                      </div>
                    </Command.Item>
                  ))}
                </Command.Group>
              </>
            )}

            {/* Default Mode - Show remaining command groups */}
            {mode === 'default' && (
              <>
                <Command.Separator className="h-px bg-zinc-200 dark:bg-zinc-700 my-2 mx-2" />

                {/* Navigation Group */}
                <Command.Group heading="Navigation">
                  {commandData.navigation.map((item) => (
                    <Command.Item
                      key={item.id}
                      value={item.id}
                      keywords={item.keywords}
                      onSelect={handleSelect}
                      className={cn(
                        "flex items-center gap-3 px-3 py-2.5 text-sm cursor-pointer rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors mx-1 mb-1",
                        // Fade keyboard selection when mouse is being used
                        isUsingMouse
                          ? "data-[selected=true]:bg-zinc-50 dark:data-[selected=true]:bg-zinc-800/50 data-[selected=true]:opacity-60"
                          : "data-[selected=true]:bg-zinc-100 dark:data-[selected=true]:bg-zinc-800"
                      )}
                    >
                      <item.icon className="w-5 h-5 text-zinc-600 dark:text-zinc-400" />
                      <div className="flex-1">
                        <span className="text-zinc-800 dark:text-zinc-200">{item.label}</span>
                      </div>
                      <div className="text-xs text-zinc-600 dark:text-zinc-400 bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">
                        Page
                      </div>
                    </Command.Item>
                  ))}
                </Command.Group>

                <Command.Separator className="h-px bg-zinc-200 dark:bg-zinc-700 my-2 mx-2" />

                {/* Recent Group */}
                <Command.Group heading="Recent">
                  {commandData.recent.map((item) => (
                    <Command.Item
                      key={item.id}
                      value={item.id}
                      keywords={item.keywords}
                      onSelect={handleSelect}
                      className={cn(
                        "flex items-center gap-3 px-3 py-2.5 text-sm cursor-pointer rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors mx-1 mb-1",
                        // Fade keyboard selection when mouse is being used
                        isUsingMouse
                          ? "data-[selected=true]:bg-zinc-50 dark:data-[selected=true]:bg-zinc-800/50 data-[selected=true]:opacity-60"
                          : "data-[selected=true]:bg-zinc-100 dark:data-[selected=true]:bg-zinc-800"
                      )}
                    >
                      <item.icon className="w-4 h-4 text-zinc-600 dark:text-zinc-400" />
                      <div className="flex-1">
                        <span className="text-zinc-800 dark:text-zinc-200">{item.label}</span>
                      </div>
                      <div className="text-xs text-zinc-600 dark:text-zinc-400 bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">
                        Recent
                      </div>
                    </Command.Item>
                  ))}
                </Command.Group>

                <Command.Separator className="h-px bg-zinc-200 dark:bg-zinc-700 my-2 mx-2" />

                {/* Suggestions Group */}
                <Command.Group heading="Suggestions">
                  {commandData.suggestions.map((item) => (
                    <Command.Item
                      key={item.id}
                      value={item.id}
                      keywords={item.keywords}
                      onSelect={handleSelect}
                      className="flex items-center gap-3 px-3 py-2.5 text-sm cursor-pointer rounded-lg hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors data-[selected=true]:bg-zinc-100 dark:data-[selected=true]:bg-zinc-800 mx-1 mb-1"
                    >
                      <item.icon className="w-4 h-4 text-zinc-600 dark:text-zinc-400" />
                      <div className="flex-1">
                        <span className="text-zinc-800 dark:text-zinc-200">{item.label}</span>
                      </div>
                      <div className="text-xs text-zinc-500 bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">
                        Tip
                      </div>
                    </Command.Item>
                  ))}
                </Command.Group>
              </>
            )}
          </Command.List>
        </Command>
      </div>

      {/* Only show the bottom search section if hideSearchInput is false */}
      {!hideSearchInput && (
        <div className="bg-zinc-50 dark:bg-zinc-900/50 border border-t-0 border-zinc-200 dark:border-zinc-700 rounded-b-lg shadow-lg">
          <div className="p-3">
            <div className="flex items-center space-x-2 mb-3">
              <Search className="h-4 w-4 text-zinc-400 dark:text-zinc-500" />
              <input
                type="text"
                placeholder={getPlaceholderText()}
                value={getCurrentInputValue()}
                onChange={(e) => hideSearchInput ? handleExternalSearchChange(e.target.value) : handleInternalSearchChange(e.target.value)}
                className="flex-1 bg-transparent border-none outline-none text-sm placeholder:text-zinc-500 dark:placeholder:text-zinc-400 text-zinc-900 dark:text-zinc-100"
                autoFocus
              />
            </div>
            
            {/* Keyboard Shortcuts */}
            <div className="flex items-center justify-between text-xs text-zinc-500 dark:text-zinc-400">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                    <span className="text-xs">↑</span>
                    <span className="text-xs">↓</span>
                  </kbd>
                  <span>navigate</span>
                </div>
                <div className="flex items-center space-x-1">
                  <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                    <span className="text-xs">↵</span>
                  </kbd>
                  <span>select</span>
                </div>
                <div className="flex items-center space-x-1">
                  <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                    <span className="text-xs">esc</span>
                  </kbd>
                  <span>close</span>
                </div>
              </div>
              <div className="flex items-center space-x-1">
                <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                  <span className="text-xs">⌘</span>
                </kbd>
                <kbd className="inline-flex h-5 select-none items-center gap-1 rounded border border-zinc-200 dark:border-zinc-600 bg-white dark:bg-zinc-700 px-1.5 font-mono text-[10px] font-medium">
                  <span className="text-xs">K</span>
                </kbd>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}