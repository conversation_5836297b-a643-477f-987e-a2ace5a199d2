'use client';
import { useState, useEffect, useCallback, useMemo, memo, useRef } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { useHotkeys } from 'react-hotkeys-hook';
import { cn } from '../../../../lib/utils';
import { useIsMobile } from '../../../../hooks/use-mobile';
import { useSharedSidebarState } from '../../../../stores/sidebar-store.client';
import { SquircleProvider } from '../../../providers/squircle-provider.client';
import {
  SidebarProvider,
  SidebarInset,
  SidebarTrigger,
} from '../../../ui/sidebar.client';
import { AppSidebar } from './app-sidebar.client';
import { RightSidebar } from '../../shared/dual-sidebar/right-sidebar';
import { useRedirect, useLocation, useParams, usePopulatePathname } from 'blade/hooks';
import { useMutation } from 'blade/client/hooks';
import { DynamicHeader } from './dynamic-header.client';
import {
  ChevronDown,
  Home,
  Bell,
  PlayCircle,
  Compass,
  Brain,
  Circle,
  Plus
} from 'lucide-react';
import { CommandMenu } from '../../shared/command-menu/command.client';
import { createPortal } from 'react-dom';
import { useAuth } from '../../../../hooks/useAuth';
import { useDialogStore } from '../../../../lib/stores/dialog.store';
import { DialogManager } from '../../../dialogs/DialogManager.client';

import { useAutoHideScrollbar } from '../../../../hooks/use-auto-hide-scrollbar';
import { useBackgroundTheme } from '../../../../hooks/use-background-theme';


// Page navigation items for the dropdown with hotkeys
interface PageNavItem {
  id: string;
  label: string;
  icon: any;
  url: string;
  description?: ((user: any) => JSX.Element);
  hotkey?: string;
}

// Helper function to get first name from full name
const getFirstName = (fullName: string | undefined): string => {
  if (!fullName) return '';
  return fullName.split(' ')[0];
};

const pageNavItems: PageNavItem[] = [
  {
    id: "home",
    label: "Home",
    icon: Home,
    url: "", // Just /teacher/[slug]
    description: (user) => (
      <h1 className="text-lg md:text-xl text-left font-redaction-normal">
        Welcome back, <span className="font-redaction">{getFirstName(user?.name || '')}!</span>
      </h1>
    ),
    hotkey: "ctrl+h,cmd+h"
  },
  {
    id: "calendar",
    label: "Calendar",
    icon: Bell,
    url: "calendar",
    description: () => (
      <h1 className="text-lg md:text-xl text-left font-redaction-normal">
        Schedule and events
      </h1>
    ),
    hotkey: "ctrl+1,cmd+1"
  },
  {
    id: "classes",
    label: "Classes",
    icon: PlayCircle,
    url: "classes",
    description: () => (
      <h1 className="text-lg md:text-xl text-left font-redaction-normal">
        Manage your classes
      </h1>
    ),
    hotkey: "ctrl+2,cmd+2"
  },
  {
    id: "discover",
    label: "Discover",
    icon: Compass,
    url: "discover",
    description: () => (
      <h1 className="text-lg md:text-xl text-left font-redaction-normal">
        Explore resources
      </h1>
    ),
    hotkey: "ctrl+3,cmd+3"
  },
  {
    id: "students",
    label: "Students",
    icon: Brain,
    url: "students",
    description: () => (
      <h1 className="text-lg md:text-xl text-left font-redaction-normal">
        Student management
      </h1>
    ),
    hotkey: "ctrl+4,cmd+4"
  },
  {
    id: "profile",
    label: "Profile",
    icon: Circle,
    url: "profile",
    description: () => (
      <h1 className="text-lg md:text-xl text-left font-redaction-normal">
        Your profile settings
      </h1>
    ),
    hotkey: "ctrl+5,cmd+5"
  }
];

interface EnhancedSidebarProps {
  children: React.ReactNode;
  className?: string;
  showUserNav?: boolean;
  showHeader?: boolean;
}

export function EnhancedSidebar({
  children,
  className,
  showUserNav = true,
  showHeader = true
}: EnhancedSidebarProps) {
  const isMobile = useIsMobile();
  const location = useLocation();
  const populatePathname = usePopulatePathname();
  const {
    isRightSidebarOpen,
    rightSidebarContent,
  } = useSharedSidebarState();

  // SCROLL CONTAINER REF for auto-focus functionality
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Add the auto-hide scrollbar functionality - applies to all pages using EnhancedSidebar
  useAutoHideScrollbar(scrollContainerRef);

  // PAGE-SPECIFIC FLYOUT STATE: Each page has its own flyout state to prevent conflicts
  const currentPageKey = populatePathname(location.pathname); // Use actual path as unique key
  const [pageSpecificFlyouts, setPageSpecificFlyouts] = useState<Record<string, string | null>>({});

  // Get flyout state for current page
  const activeFlyout = pageSpecificFlyouts[currentPageKey] || null;

  const [searchValue, setSearchValue] = useState('');
  const [showCommandMenu, setShowCommandMenu] = useState(false);
  const [commandMenuHeight, setCommandMenuHeight] = useState(0);
  const [mobileSearchActive, setMobileSearchActive] = useState(false);
  const [currentPlaceholder, setCurrentPlaceholder] = useState('Search commands');
  const [currentMode, setCurrentMode] = useState('default');
  const [currentStep, setCurrentStep] = useState<string | undefined>(undefined);
  const [formSubmitHandler, setFormSubmitHandler] = useState<(() => void) | null>(null);
  const { backgroundStyle, backgroundClassName } = useBackgroundTheme();


  const redirect = useRedirect();
  const params = useParams();
  const { slug } = useParams();
  const { user } = useAuth();
  const { openStudentDialog, openGradeLevelDialog } = useDialogStore();
  const { add } = useMutation();

  // Check if content is scrollable
  const checkScrollable = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return false;
    
    return container.scrollHeight > container.clientHeight;
  }, []);

  // Auto-focus only if content is scrollable
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer && checkScrollable()) {
      // Small delay to ensure content is rendered
      setTimeout(() => {
        scrollContainer.focus();
      }, 100);
    }
  }, [children, checkScrollable]);

  // Handle mouse enter to auto-focus (like native browser behavior)
  const handleMouseEnter = useCallback(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer && checkScrollable()) {
      scrollContainer.focus();
    }
  }, [checkScrollable]);

  // Navigation function for hotkeys
  const handlePageNavigation = useCallback((item: PageNavItem) => {
    const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];
    const fullUrl = item.url ? `/teacher/${slug}/${item.url}` : `/teacher/${slug}`;

    console.log(`🔥 Page navigation hotkey triggered for ${item.label}`);
    console.log(`🎯 Navigating to: ${fullUrl}`);
    console.log(`🔍 Current params:`, params);
    console.log(`🔍 Slug extracted:`, slug);
    
    // Close any open states
    setShowCommandMenu(false);
    setMobileSearchActive(false);
    
    redirect(fullUrl);
  }, [params, redirect]);

  // FIXED NAVIGATION HOTKEYS with better debugging
  useHotkeys('ctrl+h,cmd+h', (event) => {
    console.log('🚀 HOME HOTKEY DETECTED - Raw event:', event);
    console.log('🚀 Event details:', {
      key: event.key,
      ctrlKey: event.ctrlKey,
      metaKey: event.metaKey,
      code: event.code,
      type: event.type
    });
    
    event.preventDefault();
    event.stopPropagation();
    
    const item = pageNavItems.find(i => i.id === 'home');
    if (item) {
      console.log('🏠 Home item found, navigating...');
      handlePageNavigation(item);
    } else {
      console.error('❌ Home item not found in pageNavItems');
    }
  }, {
    enableOnFormTags: true,
    preventDefault: true,
    enableOnContentEditable: false,
    enabled: true,
    ignoreModifiers: false
  }, [params, redirect]);

  useHotkeys('ctrl+1,cmd+1', (event) => {
    console.log('🚀 CALENDAR HOTKEY DETECTED - Raw event:', event);
    console.log('🚀 Event details:', {
      key: event.key,
      ctrlKey: event.ctrlKey,
      metaKey: event.metaKey,
      code: event.code,
      type: event.type
    });
    
    event.preventDefault();
    event.stopPropagation();
    
    const item = pageNavItems.find(i => i.id === 'calendar');
    if (item) {
      console.log('📅 Calendar item found, navigating...');
      handlePageNavigation(item);
    } else {
      console.error('❌ Calendar item not found in pageNavItems');
    }
  }, {
    enableOnFormTags: true,
    preventDefault: true,
    enableOnContentEditable: false,
    enabled: true,
    ignoreModifiers: false
  }, [params, redirect]);

  useHotkeys('ctrl+2,cmd+2', (event) => {
    console.log('🚀 CLASSES HOTKEY DETECTED');
    event.preventDefault();
    event.stopPropagation();
    
    const item = pageNavItems.find(i => i.id === 'classes');
    if (item) {
      handlePageNavigation(item);
    }
  }, {
    enableOnFormTags: true,
    preventDefault: true,
    enableOnContentEditable: false,
    enabled: true,
    ignoreModifiers: false
  }, [params, redirect]);

  useHotkeys('ctrl+3,cmd+3', (event) => {
    console.log('🚀 STUDENTS HOTKEY DETECTED');
    event.preventDefault();
    event.stopPropagation();
    
    const item = pageNavItems.find(i => i.id === 'students');
    if (item) {
      handlePageNavigation(item);
    }
  }, {
    enableOnFormTags: true,
    preventDefault: true,
    enableOnContentEditable: false,
    enabled: true,
    ignoreModifiers: false
  }, [params, redirect]);

  useHotkeys('ctrl+4,cmd+4', (event) => {
    console.log('🚀 DISCOVER HOTKEY DETECTED');
    event.preventDefault();
    event.stopPropagation();
    
    const item = pageNavItems.find(i => i.id === 'discover');
    if (item) {
      handlePageNavigation(item);
    }
  }, {
    enableOnFormTags: true,
    preventDefault: true,
    enableOnContentEditable: false,
    enabled: true,
    ignoreModifiers: false
  }, [params, redirect]);

  useHotkeys('ctrl+5,cmd+5', (event) => {
    console.log('🚀 PROFILE HOTKEY DETECTED');
    event.preventDefault();
    event.stopPropagation();
    
    const item = pageNavItems.find(i => i.id === 'profile');
    if (item) {
      handlePageNavigation(item);
    }
  }, {
    enableOnFormTags: true,
    preventDefault: true,
    enableOnContentEditable: false,
    enabled: true,
    ignoreModifiers: false
  }, [params, redirect]);

  // Debug effect to log params changes
  useEffect(() => {
    console.log('🔍 Params changed:', params);
  }, [params]);

  // Debug effect to log location changes
  useEffect(() => {
    console.log('🔍 Location changed:', location);
  }, [location]);

  // Updated handleSearchToggle to manage both states
  const handleSearchToggle = useCallback((isSearchActive: boolean) => {
    console.log('🔥 handleSearchToggle called with:', isSearchActive);

    // Update both command menu and mobile search states
    setShowCommandMenu(isSearchActive);
    setMobileSearchActive(isSearchActive);

    if (!isSearchActive) {
      setSearchValue(''); // Clear search when closing
      // Reset command menu state to default
      setCurrentMode('default');
      setCurrentPlaceholder('Search commands');
      setCurrentStep(undefined);
      setFormSubmitHandler(null);
    }
  }, []);

  // Global hotkey for command menu - handled at this level for global scope
  useHotkeys('ctrl+k,cmd+k', (event) => {
    event.preventDefault();
    console.log('🔥 Global search hotkey triggered');
    
    const newSearchState = !showCommandMenu;
    console.log('🔥 New search state will be:', newSearchState);
    
    // Update both states
    setShowCommandMenu(newSearchState);
    setMobileSearchActive(newSearchState);
    
    // Notify the mobile toolbar
    handleSearchToggle(newSearchState);
    
    if (!newSearchState) {
      setSearchValue(''); // Clear search when closing
    }
  }, {
    enableOnFormTags: true,
    preventDefault: true,
    enableOnContentEditable: false,
    enabled: true,
    ignoreModifiers: false
  }, [handleSearchToggle]);

  // ESC to close command menu
  useHotkeys('escape', (event) => {
    if (showCommandMenu) {
      event.preventDefault();
      console.log('🔥 ESC - Closing command menu');
      setShowCommandMenu(false);
      setMobileSearchActive(false);
      handleSearchToggle(false);
      setSearchValue('');
      // Reset command menu state to default
      setCurrentMode('default');
      setCurrentPlaceholder('Search commands');
      setCurrentStep(undefined);
      setFormSubmitHandler(null);
    }
  }, {
    enableOnFormTags: true,
    preventDefault: false,
    enabled: true
  }, [showCommandMenu, handleSearchToggle]);

  // Dedicated hotkeys for direct dialog access
  // Using Ctrl+Shift+S for students (Ctrl+Shift+S is commonly used for "Save As" but less conflicting)
  useHotkeys('ctrl+shift+s,cmd+shift+s', (event) => {
    if (user?.role === 'teacher') {
      event.preventDefault();
      event.stopPropagation();
      console.log('🎯 Direct student dialog hotkey triggered (Ctrl+Shift+S)');
      openStudentDialog();
    }
  }, {
    enableOnFormTags: true,
    preventDefault: true,
    enableOnContentEditable: false,
    enabled: true,
    ignoreModifiers: false
  }, [user?.role, openStudentDialog]);

  // Using Ctrl+Shift+C for classes (less conflicting than Ctrl+Tab+C)
  useHotkeys('ctrl+shift+c,cmd+shift+c', (event) => {
    if (user?.role === 'teacher') {
      event.preventDefault();
      event.stopPropagation();
      console.log('🎯 Direct class dialog hotkey triggered (Ctrl+Shift+C)');
      openGradeLevelDialog();
    }
  }, {
    enableOnFormTags: true,
    preventDefault: true,
    enableOnContentEditable: false,
    enabled: true,
    ignoreModifiers: false
  }, [user?.role, openGradeLevelDialog]);

  // Add this callback to handle search value changes
  const handleSearchValueChange = useCallback((value: string) => {
    setSearchValue(value);
  }, []);

  // Handle command menu mode changes
  const handleModeChange = useCallback((mode: string, placeholder: string, step?: string) => {
    console.log('🎯 Command menu mode changed:', mode, placeholder, step);
    setCurrentPlaceholder(placeholder);
    setCurrentMode(mode);
    setCurrentStep(step);
  }, []);

  // Handle form submission callback from command menu
  const handleFormSubmitCallback = useCallback((submitHandler: () => void) => {
    setFormSubmitHandler(() => submitHandler);
  }, []);

  // Handle student addition from command menu
  const handleStudentAdd = useCallback(async (student: any) => {
    console.log('🎯 Student added from command menu:', student);

    try {
      if (!user || user.role !== 'teacher') {
        throw new Error('Only teachers can add students');
      }

      // Generate username if needed
      const username = student.generateUsername ? student.name.toLowerCase().replace(/\s+/g, '') + Math.floor(Math.random() * 100).toString().padStart(2, '0') : undefined;

      // Generate a unique password for this student
      const generateStudentPassword = () => {
        const adjectives = ['Smart', 'Bright', 'Quick', 'Sharp', 'Clever', 'Wise', 'Bold', 'Swift'];
        const nouns = ['Lion', 'Eagle', 'Tiger', 'Wolf', 'Bear', 'Fox', 'Hawk', 'Owl'];
        const numbers = Math.floor(Math.random() * 100).toString().padStart(2, '0');

        const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
        const noun = nouns[Math.floor(Math.random() * nouns.length)];

        return `${adjective}${noun}${numbers}`;
      };

      const uniquePassword = generateStudentPassword();

      // Dispatch optimistic update event for instant UI feedback
      window.dispatchEvent(new CustomEvent('studentCreationStart', {
        detail: {
          name: student.name,
          email: student.email,
          generateUsername: !!username
        }
      }));

      const response = await fetch('/api/create-student', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email: student.email,
          password: uniquePassword,
          name: student.name,
          username,
          teacherId: user.id
          // No grade or classes required initially
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));

        // Handle user already exists case - try to add them as existing student
        if (errorData.body?.code === 'USER_ALREADY_EXISTS') {
          try {
            // Check if user exists and get their data
            const checkResponse = await fetch('/api/check-user-exists', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include',
              body: JSON.stringify({ email: student.email })
            });

            if (checkResponse.ok) {
              const { exists, user: existingUser } = await checkResponse.json();
              if (exists && existingUser) {
                // Dispatch event for existing student
                window.dispatchEvent(new CustomEvent('existingStudentFound', {
                  detail: { student: existingUser }
                }));
                return; // Exit early, don't throw error
              }
            }
          } catch (checkError) {
            console.error('Error checking existing user:', checkError);
          }

          // If we can't add as existing student, show error
          throw new Error(`${student.email} already has an account`);
        } else {
          let errorMessage = errorData.error || 'Unknown error';
          if (errorMessage === 'Internal server error') {
            errorMessage = 'Failed to create student account. Please try again.';
          }
          throw new Error(errorMessage);
        }
      }

      const result = await response.json();
      console.log('🎯 Student created successfully:', result);

      // No need to redirect since we have optimistic updates
      // The student management component will handle the UI updates via events

      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('studentCreated', {
        detail: { student: result.user }
      }));

    } catch (error) {
      console.error('Error creating student:', error);
      // You might want to show a toast notification here
      window.dispatchEvent(new CustomEvent('studentCreationError', {
        detail: { error: error instanceof Error ? error.message : 'Failed to create student' }
      }));
    }
  }, [user]);

  // Handle class addition from command menu
  const handleClassAdd = useCallback(async (className: string) => {
    console.log('🎯 Class added from command menu:', className);

    // Create optimistic class object for immediate UI feedback
    const optimisticClass = {
      id: `temp-${Date.now()}`,
      name: className,
      description: `${className} class`,
      teacherId: user?.id || '',
      isActive: true,
      maxCapacity: 30,
      currentEnrollment: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      isOptimistic: true // Flag to identify optimistic updates
    };

    try {
      if (!user || user.role !== 'teacher') {
        throw new Error('Only teachers can add classes');
      }

      // Dispatch optimistic update immediately for instant UI feedback
      window.dispatchEvent(new CustomEvent('classCreated', {
        detail: {
          class: optimisticClass,
          isOptimistic: true
        }
      }));

      console.log('🚀 Optimistic class created for immediate UI feedback');

      // Use API endpoint to create class (bypasses trigger issues)
      const response = await fetch('/api/create-class', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: className,
          description: `${className} class`,
          maxCapacity: 30
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create class');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to create class');
      }

      console.log('🎯 Class created successfully in database:', result);

      // Dispatch real update to replace optimistic one
      window.dispatchEvent(new CustomEvent('classCreated', {
        detail: {
          class: result.class,
          isOptimistic: false,
          replaceOptimisticId: optimisticClass.id
        }
      }));

    } catch (error) {
      console.error('❌ Error creating class:', error);

      // Dispatch error event to remove optimistic update
      window.dispatchEvent(new CustomEvent('classCreationError', {
        detail: {
          error: error instanceof Error ? error.message : 'Failed to create class',
          removeOptimisticId: optimisticClass.id
        }
      }));
    }
  }, [user, add]);

  // Handle dialog actions from command menu (kept for backward compatibility)
  const handleDialogAction = useCallback((dialogType: string) => {
    console.log('🎯 Dialog action triggered:', dialogType);
    console.log('🎯 Available dialog functions:', { openStudentDialog, openGradeLevelDialog });

    if (dialogType === 'student') {
      console.log('🎯 Opening student dialog...');
      openStudentDialog();
    } else if (dialogType === 'grade-level') {
      console.log('🎯 Opening grade level dialog...');
      openGradeLevelDialog();
    } else {
      console.log('🎯 Unknown dialog type:', dialogType);
    }

    // Close command menu after opening dialog
    setShowCommandMenu(false);
    setMobileSearchActive(false);
    handleSearchToggle(false);
    setSearchValue('');
  }, [openStudentDialog, openGradeLevelDialog, handleSearchToggle]);

  // Debug flyout state changes
  console.log('🔍 Enhanced-sidebar flyout state:', {
    currentPageKey,
    activeFlyout,
    allPageStates: pageSpecificFlyouts
  });

  // Set flyout state for current page only
  const updateActiveFlyout = useCallback((flyoutId: string | null) => {
    console.log('🎯 Page-specific flyout update:', {
      currentPageKey,
      flyoutId,
      previousState: pageSpecificFlyouts[currentPageKey],
      allStates: pageSpecificFlyouts
    });
    setPageSpecificFlyouts(prev => ({
      ...prev,
      [currentPageKey]: flyoutId
    }));
  }, [currentPageKey, pageSpecificFlyouts]);

  // Calculate main content styles based on sidebar states - FASTER TRANSITIONS
  const mainContentStyles = useMemo(() => {
    const styles: Record<string, string> = {
      "transition-all": "true",
      "duration-100": "true", // FASTER: 100ms instead of 300ms
      "ease-out": "true", // FASTER: ease-out instead of ease-in-out
      "flex-1": "true",
      "flex": "true",
      "flex-col": "true",
      "h-full": "true",
    };

    // Right sidebar adjustments - ONLY for desktop, mobile uses Sheet overlay
    if (!isMobile && isRightSidebarOpen) {
      styles["mr-[350px]"] = "true"; // Desktop: push content left
    }

    return cn(styles);
  }, [isMobile, isRightSidebarOpen]);

  // MOVED: Get current page description to avoid IIFE in JSX - NOW WITH REGULAR H1
  const getCurrentPageDescription = useMemo(() => {
    const currentPageInfo = pageNavItems.find(item => {
      if (!location.pathname) return false;
      const pathname = location.pathname;
      if (item.url && pathname.includes(`/${item.url}`)) {
        return true;
      }
      return false;
    }) || pageNavItems[0];

    if (!currentPageInfo?.description) return null;

    // All descriptions are now functions that return JSX with regular h1 elements
    return currentPageInfo.description(user);
  }, [location.pathname, user]);

  return (
   <div 
    className="min-h-screen bg-fixed w-full"
    style={backgroundStyle}
  >
    <SquircleProvider>
      <SidebarProvider
        defaultOpenLeft={true}
        defaultOpenRight={false}
        className="min-h-screen bg-transparent" // Make the sidebar provider transparent
      >
        <div className="flex h-screen w-full overflow-hidden">
          {/* Mobile Horizontal Toolbar - Only component from AppSidebar we're using */}
          <AppSidebar
            flyout={activeFlyout}
            setFlyout={updateActiveFlyout}
            onSearchToggle={handleSearchToggle}
            searchValue={searchValue}
            onSearchValueChange={handleSearchValueChange}
            externalSearchActive={mobileSearchActive} // Pass the external state
            currentPlaceholder={currentPlaceholder} // Pass the current placeholder
            currentMode={currentMode} // Pass the current mode
            currentStep={currentStep} // Pass the current step
            formSubmitHandler={formSubmitHandler} // Pass the form submit handler
          />

          {typeof window !== 'undefined' && showCommandMenu && createPortal(
            <div className="fixed bottom-20 md:bottom-24 left-1/2 transform -translate-x-1/2 z-[100000003]">
              <div 
                className="bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-700 shadow-lg rounded-lg"
                data-command-menu="true"
                style={{
                  width: typeof window !== 'undefined' && window.innerWidth < 438 
                    ? '98vw' 
                    : 'clamp(300px, 90vw, 500px)',
                }}
                // ADD THIS: Auto-focus when the div mounts
                ref={(el) => {
                  if (el) {
                    // Small delay to ensure the component is fully rendered
                    setTimeout(() => {
                      // Find the command input and focus it
                      const input = el.querySelector('[cmdk-input]') as HTMLInputElement;
                      if (input) {
                        input.focus();
                      }
                    }, 100);
                  }
                }}
              >
                <CommandMenu
                  searchValue={searchValue}
                  onSearchChange={handleSearchValueChange}
                  onHeightChange={setCommandMenuHeight}
                  userRole={user?.role}
                  onDialogAction={handleDialogAction}
                  onStudentAdd={handleStudentAdd}
                  onClassAdd={handleClassAdd}
                  onModeChange={handleModeChange}
                  onFormSubmit={handleFormSubmitCallback}
                  onSelect={(item) => {
                    console.log('Selected item:', item);
                    const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];

                    if (item.url !== undefined) {
                      const fullUrl = item.url === ''
                        ? `/teacher/${slug}`
                        : `/teacher/${slug}/${item.url}`;
                      redirect(fullUrl);
                    }

                    setShowCommandMenu(false);
                    setMobileSearchActive(false);
                    handleSearchToggle(false);
                    setSearchValue('');
                  }}
                  onClose={() => {
                    setShowCommandMenu(false);
                    setMobileSearchActive(false);
                    handleSearchToggle(false);
                    setSearchValue('');
                  }}
                  hideSearchInput={true}
                />
              </div>
            </div>,
            document.body
          )}

          {/* Main Content Area */}
          <SidebarInset className={cn("flex-1 mx-2 md:mx-12 flex flex-col overflow-hidden", mainContentStyles)}>
            {/* Header */}
            {showHeader && (
              <header className="flex h-28 items-center gap-2 backdrop-blur-md z-10 flex-shrink-0">
                <div className="items-start">
                  <div className="flex items-center gap-4">
                       <img
                src="/logo-lightmode.png"
                alt="Penned Logo"
                className="w-7 h-7 dark:hidden pointer-events-none"
                width={26}
                height={26}
                draggable={false}
              />
              <img
                src="/logo-darkmode.png"
                alt="Penned Logo"
                className="w-7 h-7 hidden dark:block pointer-events-none"
                width={26}
                height={26}
                draggable={false}
              />
                    {/* Show current page description */}
                    {getCurrentPageDescription && (
                      <div className="ml-2 text-black dark:text-white">
                        {getCurrentPageDescription}
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Dynamic Action Buttons */}
                <div className="ml-auto">
                  <DynamicHeader />
                </div>
              </header>
            )}

            {/* Main Content with Scrolling Container - SCROLL-ONLY SCROLLBAR */}
<main className="flex-1 flex flex-col min-h-0"> {/* min-h-0 is crucial for flex child to shrink */}
  <div 
    ref={scrollContainerRef}
    className="flex-1 overflow-y-auto custom-scrollbar focus:outline-none"
    tabIndex={0} // Make it focusable
    role="main" // Accessibility
    aria-label="Main content area"
    // REMOVED: onMouseEnter={handleMouseEnter} - No mouse events for scrollbar
    style={{
      // Ensure smooth focus transition
      transition: 'all 0.1s ease-out'
    }}
  >
    <div className="max-w-full mx-auto">
      {children}
    </div>
  </div>
</main>
          </SidebarInset>
          
          {/* Right Sidebar */}
          {rightSidebarContent && (
            <RightSidebar
              content={rightSidebarContent}
            />
          )}
        </div>
      </SidebarProvider>
    </SquircleProvider>

    {/* Global Dialog Manager - Available on all teacher pages */}
    {user?.role === 'teacher' && (
      <DialogManager teacherClasses={[]} />
    )}
    </div>
  );
}